<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class KnowledgeBase extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'owner_id',
        'owner_type',
        'name',
        'type',
        'storage_path',
        'content',
        'status',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID on creation
        static::creating(function ($knowledgeBase) {
            if (empty($knowledgeBase->uuid)) {
                $knowledgeBase->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the owner of the knowledge base (polymorphic relationship).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the bots that use this knowledge base.
     */
    public function bots(): BelongsToMany
    {
        return $this->belongsToMany(Bot::class, 'bot_knowledge_bases');
    }

    /**
     * Get the conversations that use this knowledge base.
     */
    public function conversations(): BelongsToMany
    {
        return $this->belongsToMany(Conversation::class, 'conversation_knowledge_bases');
    }

    /**
     * Scope a query to only include ready knowledge bases.
     */
    public function scopeReady(Builder $query): Builder
    {
        return $query->where('status', 'ready');
    }

    /**
     * Scope a query to only include pending knowledge bases.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include processing knowledge bases.
     */
    public function scopeProcessing(Builder $query): Builder
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope a query to only include error knowledge bases.
     */
    public function scopeError(Builder $query): Builder
    {
        return $query->where('status', 'error');
    }



    /**
     * Scope a query to filter by type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by owner.
     */
    public function scopeForOwner(Builder $query, int $ownerId, string $ownerType): Builder
    {
        return $query->where('owner_id', $ownerId)
                    ->where('owner_type', $ownerType);
    }

    /**
     * Check if the knowledge base is ready for use.
     */
    public function isReady(): bool
    {
        return $this->status === 'ready';
    }

    /**
     * Check if the knowledge base is being processed.
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Check if the knowledge base has an error.
     */
    public function hasError(): bool
    {
        return $this->status === 'error';
    }

    /**
     * Check if the knowledge base is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Mark the knowledge base as processing.
     */
    public function markAsProcessing(): bool
    {
        return $this->update(['status' => 'processing']);
    }

    /**
     * Mark the knowledge base as ready.
     */
    public function markAsReady(): bool
    {
        return $this->update(['status' => 'ready']);
    }

    /**
     * Mark the knowledge base as error.
     */
    public function markAsError(string $errorMessage = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($errorMessage) {
            $metadata['error_message'] = $errorMessage;
            $metadata['error_at'] = now()->toISOString();
        }

        return $this->update([
            'status' => 'error',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Get the file size in bytes.
     */
    public function getFileSize(): ?int
    {
        if ($this->type === 'file' && $this->storage_path) {
            return $this->metadata['file_size'] ?? null;
        }

        if ($this->type === 'text' && $this->content) {
            return strlen($this->content);
        }

        return null;
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeForHumans(): string
    {
        $bytes = $this->getFileSize();
        
        if (!$bytes) {
            return 'Unknown';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Get the file URL if it's a file type.
     */
    public function getFileUrl(): ?string
    {
        if ($this->type === 'file' && $this->storage_path) {
            return Storage::url($this->storage_path);
        }

        return null;
    }

    /**
     * Get the MIME type of the file.
     */
    public function getMimeType(): ?string
    {
        return $this->metadata['mime_type'] ?? null;
    }

    /**
     * Get the number of chunks/segments.
     */
    public function getChunkCount(): int
    {
        return $this->metadata['chunk_count'] ?? 0;
    }

    /**
     * Update metadata.
     */
    public function updateMetadata(array $metadata): bool
    {
        $currentMetadata = $this->metadata ?? [];
        $newMetadata = array_merge($currentMetadata, $metadata);

        return $this->update(['metadata' => $newMetadata]);
    }

    /**
     * Delete the knowledge base and its associated file.
     */
    public function deleteWithFile(): bool
    {
        // Delete the file if it exists
        if ($this->type === 'file' && $this->storage_path && Storage::exists($this->storage_path)) {
            Storage::delete($this->storage_path);
        }

        // Delete the knowledge base record
        return $this->delete();
    }

    /**
     * Get knowledge base statistics.
     */
    public function getStats(): array
    {
        return [
            'uuid' => $this->uuid,
            'name' => $this->name,
            'type' => $this->type,
            'status' => $this->status,
            'file_size' => $this->getFileSize(),
            'file_size_human' => $this->getFileSizeForHumans(),
            'mime_type' => $this->getMimeType(),
            'chunk_count' => $this->getChunkCount(),
            'bots_count' => $this->bots()->count(),
            'conversations_count' => $this->conversations()->count(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }

    /**
     * Create a knowledge base from text content.
     */
    public static function createFromText(int $ownerId, string $ownerType, string $name, string $content): self
    {
        return static::create([
            'owner_id' => $ownerId,
            'owner_type' => $ownerType,
            'name' => $name,
            'type' => 'text',
            'content' => $content,
            'status' => 'ready',
            'metadata' => [
                'content_length' => strlen($content),
                'created_from' => 'text_input',
            ],
        ]);
    }

    /**
     * Create a knowledge base from uploaded file.
     */
    public static function createFromFile(int $ownerId, string $ownerType, string $name, string $storagePath, array $metadata = []): self
    {
        return static::create([
            'owner_id' => $ownerId,
            'owner_type' => $ownerType,
            'name' => $name,
            'type' => 'file',
            'storage_path' => $storagePath,
            'status' => 'pending',
            'metadata' => array_merge([
                'created_from' => 'file_upload',
            ], $metadata),
        ]);
    }



    /**
     * Check if knowledge base can be retrained.
     */
    public function canBeRetrained(): bool
    {
        return in_array($this->status, ['ready', 'error']);
    }

    /**
     * Update processing result from webhook.
     */
    public function updateProcessingResult(string $status, int $chunkCount = 0, ?string $error = null): bool
    {
        $metadata = array_merge($this->metadata ?? [], [
            'last_processed_at' => now()->toISOString(),
            'processing_result' => [
                'status' => $status,
                'chunk_count' => $chunkCount,
                'error' => $error,
            ]
        ]);

        return $this->update([
            'status' => $status,
            'metadata' => $metadata,
        ]);
    }



    /**
     * Generate text file path for text content.
     */
    public function generateTextFilePath(): ?string
    {
        if ($this->type !== 'text' || !$this->content) {
            return $this->storage_path;
        }

        $fileName = $this->uuid . '.txt';
        $path = "knowledge-bases/{$this->owner_id}/{$fileName}";

        Storage::put($path, $this->content);

        // Update storage_path
        $this->update(['storage_path' => $path]);

        return $path;
    }
}
