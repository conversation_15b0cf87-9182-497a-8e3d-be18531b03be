<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\Core\Traits\ResponseTrait;

class WebhookController extends Controller
{
    use ResponseTrait;

    /**
     * Handle RAG processing webhook from Python service.
     */
    public function ingest(Request $request): JsonResponse
    {
        try {
            // Validate webhook payload
            $validator = Validator::make($request->all(), [
                'fileId' => 'required|integer|exists:knowledge_bases,id',
                'status' => 'required|string|in:ready,error',
                'chunkCount' => 'nullable|integer|min:0',
                'error' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                Log::warning('Invalid webhook payload received', [
                    'payload' => $request->all(),
                    'errors' => $validator->errors()->toArray(),
                ]);

                return $this->errorResponse('Invalid payload', 400, $validator->errors());
            }

            $fileId = $request->input('fileId');
            $status = $request->input('status');
            $chunkCount = $request->input('chunkCount', 0);
            $error = $request->input('error');

            // Find knowledge base
            $knowledgeBase = KnowledgeBase::find($fileId);
            
            if (!$knowledgeBase) {
                Log::warning('Knowledge base not found for webhook', [
                    'file_id' => $fileId,
                    'payload' => $request->all(),
                ]);

                return $this->errorResponse('Knowledge base not found', 404);
            }

            // Update processing result
            $updated = $knowledgeBase->updateProcessingResult($status, $chunkCount, $error);

            if (!$updated) {
                Log::error('Failed to update knowledge base processing result', [
                    'file_id' => $fileId,
                    'status' => $status,
                    'chunk_count' => $chunkCount,
                    'error' => $error,
                ]);

                return $this->errorResponse('Failed to update processing result', 500);
            }

            Log::info('RAG processing webhook processed successfully', [
                'file_id' => $fileId,
                'status' => $status,
                'chunk_count' => $chunkCount,
                'knowledge_base_uuid' => $knowledgeBase->uuid,
            ]);

            return $this->successResponse([
                'message' => 'Webhook processed successfully',
                'file_id' => $fileId,
                'status' => $status,
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'payload' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('Internal server error', 500);
        }
    }

    /**
     * Health check endpoint for webhook service.
     */
    public function health(): JsonResponse
    {
        return $this->successResponse([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'service' => 'RAG Webhook Handler',
        ]);
    }
}
