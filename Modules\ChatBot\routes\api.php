<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\ChatBot\Http\Controllers\Auth\BotController;
use Modules\ChatBot\Http\Controllers\Auth\ChatController;
use Modules\ChatBot\Http\Controllers\Auth\ConversationController;
use Mo<PERSON><PERSON>\ChatBot\Http\Controllers\Auth\KnowledgeBaseController;
use Modules\ChatBot\Http\Controllers\Auth\MessageController;
use Modules\ChatBot\Http\Controllers\Auth\PromptController;
use Modules\ChatBot\Http\Controllers\Api\WebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Public bots (read-only access)
    Route::get('bots/public', [BotController::class, 'publicBots'])->name('api.bots.public');

    // Webhook endpoints (no auth required)
    Route::post('webhook/ingest', [WebhookController::class, 'ingest'])->name('api.webhook.ingest');
    Route::get('webhook/health', [WebhookController::class, 'health'])->name('api.webhook.health');
});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // User bot management
    Route::get('bots', [BotController::class, 'index'])->name('bots.index');
    Route::post('bots', [BotController::class, 'store'])->name('bots.store');
    Route::get('bots/{uuid}', [BotController::class, 'getByUuid'])->name('bots.uuid');
    Route::post('bots/{uuid}', [BotController::class, 'update'])->name('bots.update');
    Route::delete('bots/{uuid}', [BotController::class, 'destroy'])->name('bots.delete');

    // Conversation management

    // Prompt General
    Route::get('bot-general-prompt', [PromptController::class, 'getBotGeneralPrompt'])->name('bot-general-prompt');

    // Knowledge Base management
    Route::prefix('knowledge-bases')->name('knowledge-bases.')->group(function () {
        Route::get('/', [KnowledgeBaseController::class, 'index'])->name('index');
        Route::post('/text', [KnowledgeBaseController::class, 'storeFromText'])->name('store-text');
        Route::post('/file', [KnowledgeBaseController::class, 'storeFromFile'])->name('store-file');
        Route::post('/bulk-upload', [KnowledgeBaseController::class, 'bulkUpload'])->name('bulk-upload');
        Route::post('/retrain', [KnowledgeBaseController::class, 'retrain'])->name('retrain');
        Route::get('/{uuid}', [KnowledgeBaseController::class, 'show'])->name('show');
        Route::put('/{uuid}', [KnowledgeBaseController::class, 'update'])->name('update');
        Route::delete('/{uuid}', [KnowledgeBaseController::class, 'destroy'])->name('destroy');
        Route::post('/{uuid}/attach-to-bot', [KnowledgeBaseController::class, 'attachToBot'])->name('attach-to-bot');
    });
});


// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/conversations')->name('auth.conversations.')->group(function () {
    // Conversation management
    Route::get('/', [ConversationController::class, 'index'])->name('index');
    Route::post('/', [ConversationController::class, 'createOrUpdateConversation'])->name('store');
    Route::delete('{uuid}', [ConversationController::class, 'deleteConversation'])->name('delete-conversation');


    // Message management for conversations
    Route::get('{uuid}/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::post('{uuid}/messages', [MessageController::class, 'sendAndRespond'])->name('messages.sendAndRespond');
});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/chat')->name('auth.conversations.')->group(function () {
    // Conversation management
    Route::get('/bots', [ChatController::class, 'index'])->name('index');
});





// Admin API routes can be added later if needed
// Route::middleware(['auth', 'admin'])->prefix('v1/admin')->name('admin.')->group(function () {
//     // Admin bot management routes
// });
