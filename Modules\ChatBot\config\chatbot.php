<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RAG (Retrieval-Augmented Generation) Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for RAG system integration with Python services
    |
    */
    'rag' => [
        'queue_name' => env('RAG_QUEUE_NAME', 'rag-processing'),
        'enabled' => env('RAG_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Python Service Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for communication with Python RAG processing service
    |
    */
    'python' => [
        'rag_processing_url' => env('PYTHON_RAG_PROCESSING_URL', 'http://localhost:8001/api/v1/rag/process'),
        'timeout' => env('PYTHON_SERVICE_TIMEOUT', 30),
        'retry_attempts' => env('PYTHON_SERVICE_RETRY_ATTEMPTS', 2),
        'webhook_secret' => env('PYTHON_WEBHOOK_SECRET', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for file uploads and storage
    |
    */
    'uploads' => [
        'max_file_size' => env('KNOWLEDGE_BASE_MAX_FILE_SIZE', 10240), // KB
        'allowed_extensions' => ['pdf', 'doc', 'docx', 'txt', 'md'],
        'max_files_per_upload' => env('KNOWLEDGE_BASE_MAX_FILES_PER_UPLOAD', 10),
        'max_text_length' => env('KNOWLEDGE_BASE_MAX_TEXT_LENGTH', 1000000), // 1MB
    ],

    /*
    |--------------------------------------------------------------------------
    | Knowledge Base Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for knowledge base management
    |
    */
    'knowledge_base' => [
        'max_per_bot' => env('KNOWLEDGE_BASE_MAX_PER_BOT', 20),
        'max_per_user' => env('KNOWLEDGE_BASE_MAX_PER_USER', 100),
        'auto_process' => env('KNOWLEDGE_BASE_AUTO_PROCESS', true),
        'default_collection' => env('KNOWLEDGE_BASE_DEFAULT_COLLECTION', 'documents'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Bot Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for bot behavior and limits
    |
    */
    'bot' => [
        'max_per_user' => env('BOT_MAX_PER_USER', 50),
        'default_model' => env('BOT_DEFAULT_MODEL', 'gpt-3.5-turbo'),
        'max_knowledge_sources_on_creation' => env('BOT_MAX_KNOWLEDGE_SOURCES_ON_CREATION', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Conversation Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for conversation management
    |
    */
    'conversation' => [
        'max_per_bot' => env('CONVERSATION_MAX_PER_BOT', 1000),
        'auto_cleanup_days' => env('CONVERSATION_AUTO_CLEANUP_DAYS', 30),
        'max_messages_per_conversation' => env('CONVERSATION_MAX_MESSAGES', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for caching bot and knowledge base data
    |
    */
    'cache' => [
        'enabled' => env('CHATBOT_CACHE_ENABLED', true),
        'ttl' => env('CHATBOT_CACHE_TTL', 3600), // 1 hour
        'prefix' => env('CHATBOT_CACHE_PREFIX', 'chatbot'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for logging RAG operations
    |
    */
    'logging' => [
        'enabled' => env('RAG_LOGGING_ENABLED', true),
        'level' => env('RAG_LOGGING_LEVEL', 'info'),
        'channel' => env('RAG_LOGGING_CHANNEL', 'single'),
    ],
];
