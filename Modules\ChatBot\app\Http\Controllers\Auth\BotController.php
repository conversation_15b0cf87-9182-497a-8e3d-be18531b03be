<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Http\Requests\BotRequest;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShare;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Http\Requests\UpdateBotRequest;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class BotController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {

    }

    /**
     * Display a listing of user's accessible bots.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $bots = Bot::query()
                ->with([
                    'aiModel:id,key,name', 'owner' => function ($query) {
                    $query->select(['id', 'first_name', 'last_name', 'full_name', 'avatar']);
                },
                'conversations' => function ($query) {
                    $query->select(['id', 'bot_id', 'title', 'status', 'last_message_at'])
                        ->forOwner(auth()->id(), get_class(auth()->user()))
                        ->active()
                        ->orderBy('updated_at', 'desc')
                        ->limit(1);
                }])
                ->where([
                    'owner_id' => auth()->id(),
                    'owner_type' => get_class($user)
                ])
                ->orderByDesc('updated_at')
                ->get()
                ->map(function (Bot $bot) {
                    $bot->aiModel->makeHidden(['id']);
                    $bot->owner->makeHidden(['id']);
                    return $bot->makeHidden(['id', 'model_ai_id', 'owner_id', 'owner_type']);
                });

            return $this->successResponse($bots);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created bot.
     */
    public function store(BotRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $data = $request->all();
            $data['owner_id'] = $user->id;
            $data['owner_type'] = get_class($user);

            if ($request->hasFile('logo')) {
                $logoPath = $this->handleLogoUpload($request->file('logo'), $user->uuid);
                $data['logo'] = $logoPath;
            }

            $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'), app()->currentLocale())?->id;

            // Create bot
            $bot = Bot::create($data);

            // Process knowledge sources
            $pendingKnowledgeBaseIds = $this->processKnowledgeSources($request, $user, $bot);

            // Dispatch RAG processing job if there are pending files
            if (!empty($pendingKnowledgeBaseIds)) {
                RAGFileProcessingJob::dispatch(
                    $pendingKnowledgeBaseIds,
                    $user->id,
                    'bot_creation',
                    $bot->id
                );
            }

            return $this->successResponse($bot->makeHidden('id'),
                __('Bot created successfully'),
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Display the specified bot.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can access this bot
            if (!$bot->canBeAccessedBy($user->id)) {
                return $this->errorResponse('Access denied', 403);
            }

            $bot->load(['aiModel', 'owner']);

            return $this->successResponse(
                new BotResource($bot),
                'Bot retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function getByUuid($uuid)
    {
        $user = auth()->user();

        $bot = Bot::query()
            ->with(['aiModel:id,key,name', 'owner' => function ($query) {
                $query->select(['id', 'first_name', 'last_name', 'full_name', 'avatar']);
            }])
            ->where([
                'uuid' => $uuid,
                'owner_id' => $user->id,
                'owner_type' => get_class($user)
            ])->first();

        if (!$bot) {
            return $this->notFoundResponse();
        }

        $bot->aiModel->makeHidden(['id']);
        $bot->owner->makeHidden(['id']);
        $bot->makeHidden(['id', 'model_ai_id', 'owner_id', 'owner_type']);

        return $this->successResponse($bot);

    }

    /**
     * Update the specified bot.
     */
    public function update(BotRequest $request, string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can edit this bot
            if (!$bot->userCan($user->id, 'edit')) {
                return $this->errorResponse('Access denied', 403);
            }

            $data = $request->all();

            // Handle logo upload
            if ($request->hasFile('logo')) {
                $logoPath = $this->handleLogoUpload($request->file('logo'), $user->id);
                $data['logo'] = $logoPath;

                // Delete old logo if exists
                if ($bot->hasLogo()) {
                    $bot->deleteLogo();
                }
            }

            $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'), app()->currentLocale())?->id;
            $data['is_shareable'] = $request->input('is_shareable') == 'true' ? 1 : 0;

            $bot->update($data);

            return $this->successResponse($bot,
                'Bot updated successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified bot.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can delete this bot
            if (!$bot->userCan($user->id, 'delete')) {
                return $this->errorResponse('Access denied', 403);
            }

            $bot->delete();

            return $this->successResponse('Bot deleted successfully');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Share a bot with another user.
     */
    public function share(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'user_email' => 'required|email|exists:users,email',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can share this bot
            if (!$bot->userCan($user->id, 'share')) {
                return $this->errorResponse('You do not have permission to share this bot', 403);
            }

            // Check if bot can be shared
            if (!$bot->canBeShared()) {
                return $this->errorResponse('This bot cannot be shared', 400);
            }

            // Find target user
            $targetUser = User::where('email', $request->user_email)->first();

            if ($targetUser->id === $user->id) {
                return $this->errorResponse('You cannot share a bot with yourself', 400);
            }

            $share = $bot->shareWith($targetUser->id);

            if (!$share) {
                return $this->errorResponse('Failed to share bot', 500);
            }

            $share->load(['user', 'sharedBy']);

            return $this->successResponse(
                $share,
                'Bot shared successfully',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Unshare a bot from a user.
     */
    public function unshare(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can manage shares for this bot
            if (!$bot->userCan($user->id, 'share')) {
                return $this->errorResponse('You do not have permission to manage shares for this bot', 403);
            }

            $success = $bot->unshareFrom($request->user_id);

            if (!$success) {
                return $this->errorResponse('Bot was not shared with this user', 404);
            }

            return $this->successResponse('Bot unshared successfully');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get bot shares.
     */
    public function shares(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can view shares for this bot
            if (!$bot->userCan($user->id, 'admin')) {
                return $this->errorResponse('You do not have permission to view shares for this bot', 403);
            }

            $shares = $bot->userShares()
                ->with(['user', 'sharedBy'])
                ->orderBy('created_at', 'desc')
                ->get();

            return $this->successResponse(
                'Bot shares retrieved successfully',
                $shares
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Remove a specific share.
     */
    public function removeShare(Request $request, string $uuid, int $shareId): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can manage shares for this bot
            if (!$bot->userCan($user->id, 'share')) {
                return $this->errorResponse('You do not have permission to manage shares for this bot', 403);
            }

            $share = BotShare::where('id', $shareId)
                ->where('bot_id', $bot->id)
                ->firstOrFail();

            $share->delete();

            return $this->successResponse('Share removed successfully');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get public bots (no authentication required).
     */
    public function publicBots(Request $request): JsonResponse
    {
        $request->validate([
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $filters = $request->only(['search']);
            $perPage = $request->get('per_page', 15);

            $bots = $this->botService->getPublicBotsWithPagination($filters, $perPage);

            return $this->successResponse(
                [
                    'data' => $bots->items(),
                    'meta' => $this->getPaginationMeta($bots)
                ],
                'Public bots retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get shared bots for current user.
     */
    public function sharedWithMe(Request $request): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $perPage = $request->get('per_page', 15);

            $shares = BotShare::where('user_id', $user->id)
                ->with(['bot.aiModel', 'shareable'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return $this->successResponse(
                'Shared bots retrieved successfully',
                $shares->items(),
                $this->getPaginationMeta($shares)
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get bot usage statistics.
     */
    public function stats(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can view stats for this bot
            if (!$bot->userCan($user->id, 'read')) {
                return $this->errorResponse('Access denied', 403);
            }

            $stats = $this->botService->getBotStats($bot);

            return $this->successResponse(
                $stats,
                'Bot statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Search bots accessible by user.
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|max:255',
            'include_public' => 'nullable|boolean',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $query = $request->get('query');
            $includePublic = $request->get('include_public', true);
            $perPage = $request->get('per_page', 15);

            $bots = $this->botService->searchBotsForUser(
                $query,
                $user->id,
                get_class($user),
                $includePublic,
                $perPage
            );

            return $this->successResponse(
                [
                    'data' => $bots->items(),
                    'meta' => $this->getPaginationMeta($bots)
                ],
                'Search results retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get available tools for a bot.
     */
    public function availableTools(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can view this bot
            if (!$bot->canBeAccessedBy($user->id)) {
                return $this->errorResponse('Access denied', 403);
            }

            $tools = $this->botService->getBotAvailableTools($bot);

            return $this->successResponse(
                $tools,
                'Available tools retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get ModelAI integration info for a bot.
     */
    public function modelInfo(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can view this bot
            if (!$bot->canBeAccessedBy($user->id)) {
                return $this->errorResponse('Access denied', 403);
            }

            $modelInfo = $this->botService->getBotModelAIInfo($bot);

            return $this->successResponse(
                $modelInfo,
                'Model AI info retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Delete bot logo.
     */
    public function deleteLogo(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $uuid)->firstOrFail();

            // Check if user can edit this bot
            if (!$bot->userCan($user->id, 'edit')) {
                return $this->errorResponse('Access denied', 403);
            }

            if (!$bot->hasLogo()) {
                return $this->errorResponse('Bot does not have a logo', 400);
            }

            $bot->deleteLogo();

            return $this->successResponse(
                null,
                'Bot logo deleted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Handle logo file upload.
     */
    private function handleLogoUpload(UploadedFile $file, string $uuid): string
    {
        // Generate unique filename
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

        // Store in user-specific directory
        return $file->storeAs("bot-logos/{$uuid}", $filename, 'public');
    }

    /**
     * Get pagination metadata.
     */
    private function getPaginationMeta(LengthAwarePaginator $paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];
    }

    /**
     * Process knowledge sources for bot creation.
     */
    private function processKnowledgeSources(BotRequest $request, $user, Bot $bot): array
    {
        $pendingKnowledgeBaseIds = [];
        $knowledgeSources = $request->input('knowledge_sources', []);

        // Process new file uploads
        if (isset($knowledgeSources['new_uploads']) && is_array($knowledgeSources['new_uploads'])) {
            foreach ($knowledgeSources['new_uploads'] as $file) {
                if ($file instanceof UploadedFile) {
                    $knowledgeBase = $this->createKnowledgeBaseFromFile($user, $file);
                    $bot->knowledgeBases()->attach($knowledgeBase->id);
                    $pendingKnowledgeBaseIds[] = $knowledgeBase->id;
                }
            }
        }

        // Process text inputs
        if (isset($knowledgeSources['text_inputs']) && is_array($knowledgeSources['text_inputs'])) {
            foreach ($knowledgeSources['text_inputs'] as $textInput) {
                if (isset($textInput['name']) && isset($textInput['content'])) {
                    $knowledgeBase = KnowledgeBase::createFromText(
                        $user->id,
                        get_class($user),
                        $textInput['name'],
                        $textInput['content']
                    );
                    $bot->knowledgeBases()->attach($knowledgeBase->id);
                    $pendingKnowledgeBaseIds[] = $knowledgeBase->id;
                }
            }
        }

        // Process existing files
        if (isset($knowledgeSources['existing_files']) && is_array($knowledgeSources['existing_files'])) {
            $existingKnowledgeBases = KnowledgeBase::whereIn('id', $knowledgeSources['existing_files'])
                ->where('owner_id', $user->id)
                ->where('owner_type', get_class($user))
                ->get();

            foreach ($existingKnowledgeBases as $kb) {
                $bot->knowledgeBases()->attach($kb->id);

                // Only add to processing if not already ready
                if (!$kb->isReady()) {
                    $pendingKnowledgeBaseIds[] = $kb->id;
                }
            }
        }

        return $pendingKnowledgeBaseIds;
    }

    /**
     * Create knowledge base from uploaded file.
     */
    private function createKnowledgeBaseFromFile($user, UploadedFile $file): KnowledgeBase
    {
        // Generate storage path
        $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $storagePath = 'knowledge-bases/' . $user->id . '/' . $fileName;

        // Store the file
        $file->storeAs('knowledge-bases/' . $user->id, $fileName);

        // Prepare metadata
        $metadata = [
            'original_name' => $file->getClientOriginalName(),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'uploaded_at' => now()->toISOString(),
            'created_from' => 'bot_creation',
        ];

        // Use original filename as name
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

        return KnowledgeBase::createFromFile(
            $user->id,
            get_class($user),
            $name,
            $storagePath,
            $metadata
        );
    }
}
