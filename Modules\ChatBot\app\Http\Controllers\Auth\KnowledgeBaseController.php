<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Models\Bot;
use Modules\Core\Traits\ResponseTrait;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class KnowledgeBaseController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        // Authentication handled by routes
    }

    /**
     * Display a listing of knowledge bases.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string|in:pending,processing,ready,error',
            'type' => 'nullable|string|in:file,text',
            'search' => 'nullable|string|max:255',
        ]);

        try {
            $user = auth()->user();
            $perPage = $request->get('per_page', 15);
            
            $query = KnowledgeBase::forOwner($user->id, get_class($user));

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('search')) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }

            $knowledgeBases = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->successResponse(
                [
                    'data' => $knowledgeBases->items(),
                    'meta' => $this->getPaginationMeta($knowledgeBases)
                ],
                'Knowledge bases retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created knowledge base from text.
     */
    public function storeFromText(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string|max:1000000', // 1MB text limit
        ]);

        try {
            $user = auth()->user();

            $knowledgeBase = KnowledgeBase::createFromText(
                $user->id,
                get_class($user),
                $request->name,
                $request->content
            );

            return $this->successResponse(
                $knowledgeBase->getStats(),
                'Knowledge base created successfully from text',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created knowledge base from file upload.
     */
    public function storeFromFile(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'file' => 'required|file|mimes:txt,pdf,doc,docx,md|max:10240', // 10MB limit
        ]);

        try {
            $user = auth()->user();
            $file = $request->file('file');
            
            // Generate storage path
            $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $storagePath = 'knowledge-bases/' . $user->id . '/' . $fileName;
            
            // Store the file
            $file->storeAs('knowledge-bases/' . $user->id, $fileName);

            // Prepare metadata
            $metadata = [
                'original_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'uploaded_at' => now()->toISOString(),
            ];

            // Use provided name or original filename
            $name = $request->name ?: pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

            $knowledgeBase = KnowledgeBase::createFromFile(
                $user->id,
                get_class($user),
                $name,
                $storagePath,
                $metadata
            );

            return $this->successResponse(
                $knowledgeBase->getStats(),
                'Knowledge base created successfully from file',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Display the specified knowledge base.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                                        ->forOwner($user->id, get_class($user))
                                        ->firstOrFail();

            return $this->successResponse(
                $knowledgeBase->getStats(),
                'Knowledge base retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Update the specified knowledge base.
     */
    public function update(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string|max:1000000',
        ]);

        try {
            $user = auth()->user();
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                                        ->forOwner($user->id, get_class($user))
                                        ->firstOrFail();

            // Only allow updating text-based knowledge bases
            if ($knowledgeBase->type !== 'text') {
                return $this->errorResponse('Only text-based knowledge bases can be updated', 400);
            }

            $data = $request->only(['name', 'content']);
            
            // If content is updated, mark as ready and update metadata
            if (isset($data['content'])) {
                $data['status'] = 'ready';
                $knowledgeBase->updateMetadata([
                    'content_length' => strlen($data['content']),
                    'updated_at' => now()->toISOString(),
                ]);
            }

            $knowledgeBase->update($data);

            return $this->successResponse(
                $knowledgeBase->getStats(),
                'Knowledge base updated successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified knowledge base.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                                        ->forOwner($user->id, get_class($user))
                                        ->firstOrFail();

            $knowledgeBase->deleteWithFile();

            return $this->successResponse(
                null,
                'Knowledge base deleted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Attach knowledge base to a bot.
     */
    public function attachToBot(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'bot_uuid' => 'required|string|exists:bots,uuid',
        ]);

        try {
            $user = auth()->user();
            
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                                        ->forOwner($user->id, get_class($user))
                                        ->firstOrFail();

            $bot = Bot::where('uuid', $request->bot_uuid)->firstOrFail();

            // Check if user can edit this bot
            if (!$bot->userCan($user->id, 'edit')) {
                return $this->errorResponse('You do not have permission to edit this bot', 403);
            }

            $bot->attachKnowledgeBase($knowledgeBase->id);

            return $this->successResponse(
                null,
                'Knowledge base attached to bot successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base or bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Detach knowledge base from a bot.
     */
    public function detachFromBot(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'bot_uuid' => 'required|string|exists:bots,uuid',
        ]);

        try {
            $user = auth()->user();
            
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                                        ->forOwner($user->id, get_class($user))
                                        ->firstOrFail();

            $bot = Bot::where('uuid', $request->bot_uuid)->firstOrFail();

            // Check if user can edit this bot
            if (!$bot->userCan($user->id, 'edit')) {
                return $this->errorResponse('You do not have permission to edit this bot', 403);
            }

            $bot->detachKnowledgeBase($knowledgeBase->id);

            return $this->successResponse(
                null,
                'Knowledge base detached from bot successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base or bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get pagination meta data.
     */
    private function getPaginationMeta($paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];
    }

    /**
     * Bulk upload multiple files to knowledge base.
     */
    public function bulkUpload(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'required|array|max:10',
            'files.*' => 'file|max:10240|mimes:pdf,doc,docx,txt,md',
        ]);

        try {
            $user = auth()->user();
            $files = $request->file('files');
            $knowledgeBaseIds = [];

            foreach ($files as $file) {
                // Generate storage path
                $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
                $storagePath = 'knowledge-bases/' . $user->id . '/' . $fileName;

                // Store the file
                $file->storeAs('knowledge-bases/' . $user->id, $fileName);

                // Prepare metadata
                $metadata = [
                    'original_name' => $file->getClientOriginalName(),
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'uploaded_at' => now()->toISOString(),
                    'created_from' => 'standalone_upload',
                ];

                // Use original filename as name
                $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

                $knowledgeBase = KnowledgeBase::createFromFile(
                    $user->id,
                    get_class($user),
                    $name,
                    $storagePath,
                    $metadata
                );

                $knowledgeBaseIds[] = $knowledgeBase->id;
            }

            // Dispatch RAG processing job
            RAGFileProcessingJob::dispatch(
                $knowledgeBaseIds,
                $user->id,
                'standalone_upload'
            );

            return $this->successResponse([
                'message' => 'Files uploaded successfully and queued for processing',
                'count' => count($knowledgeBaseIds),
                'knowledge_base_ids' => $knowledgeBaseIds,
            ], 'Files uploaded successfully', 201);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Retrain selected knowledge bases.
     */
    public function retrain(Request $request): JsonResponse
    {
        $request->validate([
            'knowledge_base_ids' => 'required|array|max:20',
            'knowledge_base_ids.*' => 'integer|exists:knowledge_bases,id',
        ]);

        try {
            $user = auth()->user();
            $knowledgeBaseIds = $request->input('knowledge_base_ids');

            // Verify ownership and check if files can be retrained
            $knowledgeBases = KnowledgeBase::whereIn('id', $knowledgeBaseIds)
                ->where('owner_id', $user->id)
                ->where('owner_type', get_class($user))
                ->get();

            if ($knowledgeBases->count() !== count($knowledgeBaseIds)) {
                return $this->errorResponse('Some knowledge bases not found or access denied', 404);
            }

            // Check if all can be retrained
            $cannotRetrain = $knowledgeBases->filter(function ($kb) {
                return !$kb->canBeRetrained();
            });

            if ($cannotRetrain->isNotEmpty()) {
                return $this->errorResponse(
                    'Some knowledge bases cannot be retrained. Only ready or error status files can be retrained.',
                    400
                );
            }

            // Update status to pending
            $knowledgeBases->each(function ($kb) {
                $kb->update([
                    'status' => 'pending',
                    'metadata' => array_merge($kb->metadata ?? [], [
                        'retrain_requested_at' => now()->toISOString(),
                    ])
                ]);
            });

            // Dispatch RAG processing job
            RAGFileProcessingJob::dispatch(
                $knowledgeBaseIds,
                $user->id,
                'retrain'
            );

            return $this->successResponse([
                'message' => 'Knowledge bases queued for retraining',
                'count' => count($knowledgeBaseIds),
                'knowledge_base_ids' => $knowledgeBaseIds,
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
