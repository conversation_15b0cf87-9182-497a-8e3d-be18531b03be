<?php

namespace Modules\ChatBot\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\KnowledgeBase;

class RAGFileProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $retryAfter = 60;
    public $timeout = 300;

    public function __construct(
        public array $knowledgeBaseIds,
        public int $ownerId,
        public string $operation = 'process', // 'process', 'retrain', 'bot_creation', 'standalone_upload'
        public ?int $botId = null
    ) {
        $this->onQueue(config('chatbot.rag.queue_name', 'rag-processing'));
    }

    public function handle(): void
    {
        try {
            $taskId = (string) Str::uuid();
            
            // Load knowledge bases
            $knowledgeBases = KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
                ->where('owner_id', $this->ownerId)
                ->get();

            if ($knowledgeBases->isEmpty()) {
                Log::warning('No knowledge bases found for processing', [
                    'knowledge_base_ids' => $this->knowledgeBaseIds,
                    'owner_id' => $this->ownerId,
                ]);
                return;
            }

            // Update status to processing
            $knowledgeBases->each(function ($kb) {
                $kb->update([
                    'status' => 'processing',
                    'metadata' => array_merge($kb->metadata ?? [], [
                        'processing_started_at' => now()->toISOString(),
                        'operation' => $this->operation,
                        'task_id' => $this->job->uuid(),
                    ])
                ]);
            });

            // Prepare payload
            $payload = $this->buildPayload($taskId, $knowledgeBases);

            // Send to Python service
            $response = Http::timeout(config('chatbot.python.timeout', 30))
                ->retry(config('chatbot.python.retry_attempts', 2), 1000)
                ->post(config('chatbot.python.rag_processing_url'), $payload);

            if (!$response->successful()) {
                throw new \Exception("Python service error: " . $response->body());
            }

            Log::info('RAG processing job dispatched successfully', [
                'task_id' => $taskId,
                'knowledge_base_ids' => $this->knowledgeBaseIds,
                'operation' => $this->operation,
                'bot_id' => $this->botId,
            ]);

        } catch (\Exception $e) {
            Log::error('RAG processing job failed', [
                'knowledge_base_ids' => $this->knowledgeBaseIds,
                'operation' => $this->operation,
                'error' => $e->getMessage(),
            ]);

            // Update status to error
            KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
                ->update([
                    'status' => 'error',
                    'metadata->error' => $e->getMessage(),
                    'metadata->failed_at' => now()->toISOString(),
                ]);

            throw $e;
        }
    }

    private function buildPayload(string $taskId, $knowledgeBases): array
    {
        $files = $knowledgeBases->map(function ($kb) {
            return [
                'fileId' => $kb->id,
                'storagePath' => $this->getStoragePath($kb),
                'url' => $this->getFileUrl($kb),
            ];
        })->toArray();

        return [
            'taskId' => $taskId,
            'type' => 'file',
            'ownerId' => $this->ownerId,
            'collection' => 'documents', // hoặc có thể là "user_{$this->ownerId}"
            'files' => $files,
            'webhookUrl' => route('api.webhook.ingest'),
        ];
    }

    private function getStoragePath(KnowledgeBase $kb): string
    {
        // Tạo file tạm cho text content nếu cần
        if ($kb->type === 'text' && $kb->content && !$kb->storage_path) {
            return $kb->generateTextFilePath();
        }
        
        return $kb->storage_path ?? '';
    }

    private function getFileUrl(KnowledgeBase $kb): string
    {
        $storagePath = $this->getStoragePath($kb);
        
        if ($storagePath) {
            return Storage::url($storagePath);
        }
        
        return '';
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('RAG processing job failed permanently', [
            'knowledge_base_ids' => $this->knowledgeBaseIds,
            'owner_id' => $this->ownerId,
            'operation' => $this->operation,
            'bot_id' => $this->botId,
            'error' => $exception->getMessage(),
        ]);

        // Update all knowledge bases to error status
        KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
            ->update([
                'status' => 'error',
                'metadata->error' => $exception->getMessage(),
                'metadata->failed_at' => now()->toISOString(),
                'metadata->retry_count' => $this->attempts(),
            ]);
    }
}
