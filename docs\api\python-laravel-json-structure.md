# JSON Structure - <PERSON><PERSON> ↔ Python Communication

## 1. Request từ <PERSON><PERSON> đến Python (Embedding & Query)

### Endpoint: `POST /api/v1/embedding/query`

```json
{
  "message_id": 12345,
  "question": "<PERSON><PERSON> là gì và cách sử dụng như thế nào?",
  "conversation_id": 67890,
  "bot_id": 123,
  "webhook_url": "https://your-domain.com/api/v1/webhooks/embedding/result",
  "webhook_secret": "your-webhook-secret-key",

  "knowledge_base_ids": [456, 789],
  "user_id": 123,
  "language": "vi",

  "options": {
    "max_results": 10,
    "similarity_threshold": 0.7,
    "include_metadata": true
  },

  "metadata": {
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-15T10:35:00Z"
  }
}
```

## 2. Response từ Python v<PERSON> (Webhook)

### Endpoint: `POST /api/v1/webhooks/embedding/result`

```json
{
  "message_id": 12345,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "success",
  "processing_time": 2.5,

  "context_data": [
    {
      "content": "Laravel là một PHP framework mã nguồn mở...",
      "score": 0.95,
      "source": "laravel_docs_introduction.md"
    },
    {
      "content": "Cài đặt Laravel: composer create-project laravel/laravel",
      "score": 0.87,
      "source": "laravel_docs_installation.md"
    }
  ],

  "metadata": {
    "total_documents_searched": 150,
    "documents_found": 25,
    "search_time_ms": 150
  }
}
```

## 3. Error Response từ Python

```json
{
  "message_id": 12345,
  "conversation_id": 67890,
  "bot_id": 123,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  
  "status": "error",
  "error": {
    "code": "EMBEDDING_FAILED",
    "message": "Failed to generate embedding for the query",
    "details": "OpenAI API rate limit exceeded",
    "timestamp": "2024-01-15T10:35:01Z"
  },
  
  "metadata": {
    "processing_time": 1.2,
    "retry_count": 3,
    "last_attempt_at": "2024-01-15T10:35:01Z"
  }
}
```

## 4. Webhook Security

### Headers Required:
- `X-Webhook-Signature`: SHA256 HMAC của request body
- `Content-Type`: `application/json`
- `User-Agent`: `Python-Embedding-Service/1.0`

### Signature Calculation:
```python
import hmac
import hashlib

def generate_signature(payload, secret):
    signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return f"sha256={signature}"
```

## 5. Status Codes

### Request to Python:
- `200`: Success - Request accepted and processing
- `400`: Bad Request - Invalid payload
- `401`: Unauthorized - Invalid API key
- `429`: Rate Limited - Too many requests
- `500`: Internal Error - Python service error

### Webhook Response:
- `200`: Success - Webhook processed successfully
- `400`: Bad Request - Invalid webhook payload
- `401`: Unauthorized - Invalid signature
- `422`: Validation Error - Missing required fields
- `500`: Internal Error - Laravel processing error
