<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Validation\Rule;
use Mo<PERSON><PERSON>\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\ToolCallingMode;
use Modules\Core\Http\Requests\BaseFormRequest;

/**
 * @property mixed $id
 * @property mixed $bot_id
 * @property mixed $parameters
 */
class BotRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:120',
            ],
            'description' => [
                'nullable',
                'string',
                'max:65535',
            ],
            'model' => [
                'required',
                'string',
                'exists:model_ai,key',
            ],
            'system_prompt' => [
                'required',
                'string',
                'max:65535',
            ],
            'parameters' => [
                'nullable',
                'array',
            ],
            'parameters.temperature' => [
                'nullable',
                'numeric',
                'min:0',
                'max:2',
            ],
            'parameters.max_tokens' => [
                'nullable',
                'integer',
                'min:1',
                'max:100000',
            ],
            'parameters.top_p' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1',
            ],
            'parameters.frequency_penalty' => [
                'nullable',
                'numeric',
                'min:-2',
                'max:2',
            ],
            'parameters.presence_penalty' => [
                'nullable',
                'numeric',
                'min:-2',
                'max:2',
            ],
            'tool_calling_mode' => [
                'nullable',
                'string',
                Rule::in(ToolCallingMode::values()),
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(BotStatus::values()),
            ],
            'metadata' => [
                'nullable',
                'array',
            ],
            // Knowledge Sources validation
            'knowledge_sources' => [
                'nullable',
                'array',
            ],
            'knowledge_sources.new_uploads' => [
                'nullable',
                'array',
                'max:10',
            ],
            'knowledge_sources.new_uploads.*' => [
                'file',
                'max:10240', // 10MB
                'mimes:pdf,doc,docx,txt,md',
            ],
            'knowledge_sources.text_inputs' => [
                'nullable',
                'array',
                'max:5',
            ],
            'knowledge_sources.text_inputs.*.name' => [
                'required',
                'string',
                'max:255',
            ],
            'knowledge_sources.text_inputs.*.content' => [
                'required',
                'string',
                'max:1000000', // 1MB text limit
            ],
            'knowledge_sources.existing_files' => [
                'nullable',
                'array',
                'max:20',
            ],
            'knowledge_sources.existing_files.*' => [
                'integer',
                'exists:knowledge_bases,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('Bot name'),
            'description' => __('Description'),
            'owner_id' => __('Owner ID'),
            'owner_type' => __('Owner type'),
            'model_ai_id' => __('AI model'),
            'system_prompt' => __('System prompt'),
            'parameters' => __('Parameters'),
            'parameters.temperature' => __('Temperature'),
            'parameters.max_tokens' => __('Max tokens'),
            'parameters.top_p' => __('Top P'),
            'parameters.frequency_penalty' => __('Frequency penalty'),
            'parameters.presence_penalty' => __('Presence penalty'),
            'tool_calling_mode' => __('Tool calling mode'),
            'status' => __('Status'),
            'is_shareable' => __('Is shareable'),
            'metadata' => __('Metadata'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('The bot name is required.'),
            'name.max' => __('The bot name may not be greater than 120 characters.'),
            'description.max' => __('The description is too long.'),
            'owner_id.required' => __('The owner ID is required.'),
            'owner_id.integer' => __('The owner ID must be a number.'),
            'owner_type.required' => __('The owner type is required.'),
            'ai_model_id.required' => __('The AI model is required.'),
            'ai_model_id.exists' => __('The selected AI model does not exist.'),
            'system_prompt.required' => __('The system prompt is required.'),
            'system_prompt.max' => __('The system prompt is too long.'),
            'parameters.array' => __('The parameters must be a valid array.'),
            'parameters.temperature.numeric' => __('The temperature must be a number.'),
            'parameters.temperature.min' => __('The temperature must be at least 0.'),
            'parameters.temperature.max' => __('The temperature may not be greater than 2.'),
            'parameters.max_tokens.integer' => __('The max tokens must be a number.'),
            'parameters.max_tokens.min' => __('The max tokens must be at least 1.'),
            'parameters.max_tokens.max' => __('The max tokens may not be greater than 100000.'),
            'parameters.top_p.numeric' => __('The top P must be a number.'),
            'parameters.top_p.min' => __('The top P must be at least 0.'),
            'parameters.top_p.max' => __('The top P may not be greater than 1.'),
            'parameters.frequency_penalty.numeric' => __('The frequency penalty must be a number.'),
            'parameters.frequency_penalty.min' => __('The frequency penalty must be at least -2.'),
            'parameters.frequency_penalty.max' => __('The frequency penalty may not be greater than 2.'),
            'parameters.presence_penalty.numeric' => __('The presence penalty must be a number.'),
            'parameters.presence_penalty.min' => __('The presence penalty must be at least -2.'),
            'parameters.presence_penalty.max' => __('The presence penalty may not be greater than 2.'),
            'tool_calling_mode.required' => __('The tool calling mode is required.'),
            'tool_calling_mode.in' => __('The selected tool calling mode is invalid.'),
            'status.required' => __('The status is required.'),
            'status.in' => __('The selected status is invalid.'),
            'metadata.array' => __('The metadata must be a valid array.'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up parameters array
        if ($this->has('parameters') && is_array($this->parameters)) {
            $parameters = array_filter($this->parameters, function ($value) {
                return $value !== null && $value !== '';
            });

            $this->merge(['parameters' => $parameters ?: null]);
        }

        // Clean up metadata array
        if ($this->has('metadata') && is_array($this->metadata)) {
            $metadata = array_filter($this->metadata, function ($value) {
                return $value !== null && $value !== '';
            });

            $this->merge(['metadata' => $metadata ?: null]);
        }
    }
}
